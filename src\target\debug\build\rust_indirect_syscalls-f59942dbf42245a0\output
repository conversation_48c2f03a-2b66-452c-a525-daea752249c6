cargo:rerun-if-changed=src/asm/callr12.asm
cargo:rerun-if-changed=src/asm/callme.asm
cargo:rerun-if-changed=src/asm/spoof.asm
OUT_DIR = Some(C:\Users\<USER>\Documents\a\rust_reimplementation\src\target\debug\build\rust_indirect_syscalls-f59942dbf42245a0\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\Users\<USER>\Documents\a\rust_reimplementation\src\target\debug\deps;C:\Users\<USER>\Documents\a\rust_reimplementation\src\target\debug;C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;%PATH%;C:\Users\<USER>\AppData\Local\bin\NASM\;C:\Windows\System32;%PATH%;C:\Users\<USER>\AppData\Local\bin\NASM\;C:\Windows\System32;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Program Files\PowerShell\7-preview\preview;C:\Program Files\Void\bin;C:\Users\<USER>\.cargo\bin;c:\Users\<USER>\AppData\Local\Programs\Trae CN\bin;"C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\bin\amd64;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin";C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,lahfsahf,sse,sse2,sse3,x87)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
 Assembling: src/asm/callr12.asm
 Assembling: src/asm/callme.asm
 Assembling: src/asm/spoof.asm
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.31.31103\atlmfc\lib\x64
cargo:rustc-link-lib=static=koneko_asm
cargo:rustc-link-search=native=C:\Users\<USER>\Documents\a\rust_reimplementation\src\target\debug\build\rust_indirect_syscalls-f59942dbf42245a0\out
cargo:rustc-link-lib=static=koneko_asm
