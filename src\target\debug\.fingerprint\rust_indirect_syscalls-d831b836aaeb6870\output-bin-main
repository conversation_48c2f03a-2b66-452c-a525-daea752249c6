{"$message_type":"diagnostic","message":"unused imports: `IMAGE_DIRECTORY_ENTRY_EXPORT` and `IMAGE_NT_HEADERS64`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":643,"byte_end":671,"line_start":22,"line_end":22,"column_start":5,"column_end":33,"is_primary":true,"text":[{"text":"    IMAGE_DIRECTORY_ENTRY_EXPORT, IMAGE_NT_HEADERS64, ReadProcessMemory, WriteProcessMemory,","highlight_start":5,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":673,"byte_end":691,"line_start":22,"line_end":22,"column_start":35,"column_end":53,"is_primary":true,"text":[{"text":"    IMAGE_DIRECTORY_ENTRY_EXPORT, IMAGE_NT_HEADERS64, ReadProcessMemory, WriteProcessMemory,","highlight_start":35,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":643,"byte_end":693,"line_start":22,"line_end":22,"column_start":5,"column_end":55,"is_primary":true,"text":[{"text":"    IMAGE_DIRECTORY_ENTRY_EXPORT, IMAGE_NT_HEADERS64, ReadProcessMemory, WriteProcessMemory,","highlight_start":5,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `IMAGE_DIRECTORY_ENTRY_EXPORT` and `IMAGE_NT_HEADERS64`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:22:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    IMAGE_DIRECTORY_ENTRY_EXPORT, IMAGE_NT_HEADERS64, ReadProcessMemory, WriteProcessMemory,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `Logger::info` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":10704,"byte_end":10800,"line_start":279,"line_end":279,"column_start":5,"column_end":101,"is_primary":true,"text":[{"text":"    app().lock().unwrap().logger.info(&format!(\"Shellcode deobfuscated ({} bytes).\", shellcode_len));","highlight_start":5,"highlight_end":101}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `Logger::info` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:279:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app().lock().unwrap().logger.info(&format!(\"Shellcode deobfuscated ({} bytes).\", shellcode_len));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `Logger::info` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":12797,"byte_end":12872,"line_start":327,"line_end":327,"column_start":5,"column_end":80,"is_primary":true,"text":[{"text":"    ctx_guard.logger.info(&format!(\"Shellcode injected at {:p}\", base_address));","highlight_start":5,"highlight_end":80}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `Logger::info` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:327:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m327\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ctx_guard.logger.info(&format!(\"Shellcode injected at {:p}\", base_address));\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `Logger::info` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":13220,"byte_end":13288,"line_start":338,"line_end":338,"column_start":9,"column_end":77,"is_primary":true,"text":[{"text":"        app().lock().unwrap().logger.info(\"Executing shellcode directly...\");","highlight_start":9,"highlight_end":77}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `Logger::info` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:338:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m338\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        app().lock().unwrap().logger.info(\"Executing shellcode directly...\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `Logger::info` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":13584,"byte_end":13655,"line_start":345,"line_end":345,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        app().lock().unwrap().logger.info(\"Setting up fibers for execution...\");","highlight_start":9,"highlight_end":80}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `Logger::info` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:345:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m345\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        app().lock().unwrap().logger.info(\"Setting up fibers for execution...\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `Logger::info` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":17313,"byte_end":17378,"line_start":434,"line_end":434,"column_start":5,"column_end":70,"is_primary":true,"text":[{"text":"    app().lock().unwrap().logger.info(\"Hooking Sleep and SleepEx...\");","highlight_start":5,"highlight_end":70}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `Logger::info` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:434:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m434\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    app().lock().unwrap().logger.info(\"Hooking Sleep and SleepEx...\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `Logger::info` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":17822,"byte_end":17872,"line_start":444,"line_end":444,"column_start":9,"column_end":59,"is_primary":true,"text":[{"text":"        app().lock().unwrap().logger.info(\"Sleep hooked.\");","highlight_start":9,"highlight_end":59}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `Logger::info` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:444:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m444\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        app().lock().unwrap().logger.info(\"Sleep hooked.\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `Logger::info` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":18002,"byte_end":18054,"line_start":448,"line_end":448,"column_start":9,"column_end":61,"is_primary":true,"text":[{"text":"        app().lock().unwrap().logger.info(\"SleepEx hooked.\");","highlight_start":9,"highlight_end":61}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `Logger::info` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:448:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m448\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        app().lock().unwrap().logger.info(\"SleepEx hooked.\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":28611,"byte_end":28660,"line_start":663,"line_end":663,"column_start":38,"column_end":87,"is_primary":true,"text":[{"text":"        let c_hz_wu_uo_lp_ksh_ezso = encode_pointer(0x4831c94881e9d4ff as *mut c_void);","highlight_start":38,"highlight_end":87}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:663:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m663\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let c_hz_wu_uo_lp_ksh_ezso = encode_pointer(0x4831c94881e9d4ff as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":28695,"byte_end":28744,"line_start":664,"line_end":664,"column_start":33,"column_end":82,"is_primary":true,"text":[{"text":"        let qzmcczftlrofp_mbk = encode_pointer(0xffff488d05efffff as *mut c_void);","highlight_start":33,"highlight_end":82}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:664:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m664\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let qzmcczftlrofp_mbk = encode_pointer(0xffff488d05efffff as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":28784,"byte_end":28833,"line_start":665,"line_end":665,"column_start":38,"column_end":87,"is_primary":true,"text":[{"text":"        let bn_fp_xx_ut_dh_zx_fbou = encode_pointer(0xff48bb44f6a40b5f as *mut c_void);","highlight_start":38,"highlight_end":87}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:665:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m665\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let bn_fp_xx_ut_dh_zx_fbou = encode_pointer(0xff48bb44f6a40b5f as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":28871,"byte_end":28920,"line_start":666,"line_end":666,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let xxn_my_wi_olk_znxquw = encode_pointer(0x895d7f4831582748 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:666:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m666\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let xxn_my_wi_olk_znxquw = encode_pointer(0x895d7f4831582748 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":28959,"byte_end":29008,"line_start":667,"line_end":667,"column_start":37,"column_end":86,"is_primary":true,"text":[{"text":"        let ma_fi_re_qdzf_rf_wrty = encode_pointer(0x2df8ffffffe2f4b8 as *mut c_void);","highlight_start":37,"highlight_end":86}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:667:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m667\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let ma_fi_re_qdzf_rf_wrty = encode_pointer(0x2df8ffffffe2f4b8 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29046,"byte_end":29095,"line_start":668,"line_end":668,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let rd_uzg_sea_eks_hkbzw = encode_pointer(0xbe27efaf619d7f44 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:668:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m668\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let rd_uzg_sea_eks_hkbzw = encode_pointer(0xbe27efaf619d7f44 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29132,"byte_end":29181,"line_start":669,"line_end":669,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let bqaq_zee_aepn_hxcha = encode_pointer(0xf6e55a1ed90f2e12 as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:669:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m669\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let bqaq_zee_aepn_hxcha = encode_pointer(0xf6e55a1ed90f2e12 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29220,"byte_end":29269,"line_start":670,"line_end":670,"column_start":37,"column_end":86,"is_primary":true,"text":[{"text":"        let p_ef_fdh_eq_fd_qpoqch = encode_pointer(0xbe95d93ac1d62d24 as *mut c_void);","highlight_start":37,"highlight_end":86}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:670:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m670\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let p_ef_fdh_eq_fd_qpoqch = encode_pointer(0xbe95d93ac1d62d24 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29307,"byte_end":29356,"line_start":671,"line_end":671,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let wol_bfa_oy_kce_kudyg = encode_pointer(0xbe2f5947c1d62d64 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:671:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m671\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let wol_bfa_oy_kce_kudyg = encode_pointer(0xbe2f5947c1d62d64 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29393,"byte_end":29442,"line_start":672,"line_end":672,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let uwi_zkxhkh_efn_ektm = encode_pointer(0xbe2f790fc152c80e as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:672:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m672\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let uwi_zkxhkh_efn_ektm = encode_pointer(0xbe2f790fc152c80e as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29480,"byte_end":29529,"line_start":673,"line_end":673,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let fml_gr_bqb_lhph_goeo = encode_pointer(0xbce93a96c16cbfe8 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:673:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m673\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let fml_gr_bqb_lhph_goeo = encode_pointer(0xbce93a96c16cbfe8 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29567,"byte_end":29616,"line_start":674,"line_end":674,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let yxp_db_uec_vex_phxij = encode_pointer(0xcac5775da57d3e85 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:674:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m674\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let yxp_db_uec_vex_phxij = encode_pointer(0xcac5775da57d3e85 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29653,"byte_end":29702,"line_start":675,"line_end":675,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let mzg_gjmo_ailvg_ctyd = encode_pointer(0x3fa94a5e48bf9216 as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:675:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m675\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mzg_gjmo_ailvg_ctyd = encode_pointer(0x3fa94a5e48bf9216 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29740,"byte_end":29789,"line_start":676,"line_end":676,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let gur_eat_zzc_vzvi_zys = encode_pointer(0xb7f543d4db7df406 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:676:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m676\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let gur_eat_zzc_vzvi_zys = encode_pointer(0xb7f543d4db7df406 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29826,"byte_end":29875,"line_start":677,"line_end":677,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let hnpl_zlt_yvpp_espst = encode_pointer(0xcaec0a8f02ddf744 as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:677:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m677\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let hnpl_zlt_yvpp_espst = encode_pointer(0xcaec0a8f02ddf744 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29912,"byte_end":29961,"line_start":678,"line_end":678,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let xcg_wvkn_cyv_rsvuhz = encode_pointer(0xf6a443da4929180c as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:678:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m678\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let xcg_wvkn_cyv_rsvuhz = encode_pointer(0xf6a443da4929180c as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":29997,"byte_end":30046,"line_start":679,"line_end":679,"column_start":34,"column_end":83,"is_primary":true,"text":[{"text":"        let umughcyda_jut_ahrt = encode_pointer(0xf7745bd4c1453bcf as *mut c_void);","highlight_start":34,"highlight_end":83}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:679:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m679\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let umughcyda_jut_ahrt = encode_pointer(0xf7745bd4c1453bcf as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30084,"byte_end":30133,"line_start":680,"line_end":680,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let rq_cqv_wai_ne_dobank = encode_pointer(0xb684425e59be290c as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:680:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m680\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let rq_cqv_wai_ne_dobank = encode_pointer(0xb684425e59be290c as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30171,"byte_end":30220,"line_start":681,"line_end":681,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let ax_owfj_de_hhm_dusta = encode_pointer(0x096d4ad4bdd53745 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:681:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m681\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let ax_owfj_de_hhm_dusta = encode_pointer(0x096d4ad4bdd53745 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30258,"byte_end":30307,"line_start":682,"line_end":682,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let pzy_vuw_kmk_iqw_wsah = encode_pointer(0x20e93a96c16cbfe8 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:682:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m682\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let pzy_vuw_kmk_iqw_wsah = encode_pointer(0x20e93a96c16cbfe8 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30345,"byte_end":30394,"line_start":683,"line_end":683,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let uka_eux_ba_mhc_fvhre = encode_pointer(0xb765c252c85cbe7c as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:683:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m683\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let uka_eux_ba_mhc_fvhre = encode_pointer(0xb765c252c85cbe7c as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30431,"byte_end":30480,"line_start":684,"line_end":684,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let gpbjm_zmx_izd_gdxbs = encode_pointer(0x16d1fa138a115b4c as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:684:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m684\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let gpbjm_zmx_izd_gdxbs = encode_pointer(0x16d1fa138a115b4c as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30518,"byte_end":30567,"line_start":685,"line_end":685,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let a_eub_bql_vlq_lgcpmm = encode_pointer(0xb39dda2a51053bcf as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:685:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m685\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let a_eub_bql_vlq_lgcpmm = encode_pointer(0xb39dda2a51053bcf as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30604,"byte_end":30653,"line_start":686,"line_end":686,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let hkzol_wqs_fhe_axocq = encode_pointer(0xb680425e593b3ecf as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:686:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m686\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let hkzol_wqs_fhe_axocq = encode_pointer(0xb680425e593b3ecf as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30690,"byte_end":30739,"line_start":687,"line_end":687,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let rgrpg_ust_dcgn_rsxx = encode_pointer(0xfaec4fd4c9413645 as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:687:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m687\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let rgrpg_ust_dcgn_rsxx = encode_pointer(0xfaec4fd4c9413645 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30777,"byte_end":30826,"line_start":688,"line_end":688,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let uki_ku_ewp_ihq_sbzed = encode_pointer(0x26e5805b01157e94 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:688:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m688\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let uki_ku_ewp_ihq_sbzed = encode_pointer(0x26e5805b01157e94 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30863,"byte_end":30912,"line_start":689,"line_end":689,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let utr_djv_dgki_lgoqiz = encode_pointer(0xb7fc4a07d7042505 as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:689:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m689\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let utr_djv_dgki_lgoqiz = encode_pointer(0xb7fc4a07d7042505 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":30950,"byte_end":30999,"line_start":690,"line_end":690,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let jm_ra_vonp_gri_cdgil = encode_pointer(0xaee5521ed315fca8 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:690:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m690\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let jm_ra_vonp_gri_cdgil = encode_pointer(0xaee5521ed315fca8 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31037,"byte_end":31086,"line_start":691,"line_end":691,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let ptg_vgo_hio_fol_vctp = encode_pointer(0xd6e559a069053e1d as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:691:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m691\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let ptg_vgo_hio_fol_vctp = encode_pointer(0xd6e559a069053e1d as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31123,"byte_end":31172,"line_start":692,"line_end":692,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let jjm_vrmn_tsof_jshuq = encode_pointer(0xacec804d600a80bb as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:692:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m692\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let jjm_vrmn_tsof_jshuq = encode_pointer(0xacec804d600a80bb as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31210,"byte_end":31259,"line_start":693,"line_end":693,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let ec_thxo_pqv_geo_pdty = encode_pointer(0x09f943e5885d7f44 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:693:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m693\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let ec_thxo_pqv_geo_pdty = encode_pointer(0x09f943e5885d7f44 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31297,"byte_end":31346,"line_start":694,"line_end":694,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let kqv_ebh_xzw_hqo_rilq = encode_pointer(0xf6a40b5fc1d0f245 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:694:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m694\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let kqv_ebh_xzw_hqo_rilq = encode_pointer(0xf6a40b5fc1d0f245 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31384,"byte_end":31433,"line_start":695,"line_end":695,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let rur_hyj_hgc_zzs_kdew = encode_pointer(0xf7a40b1e336cf42b as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:695:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m695\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let rur_hyj_hgc_zzs_kdew = encode_pointer(0xf7a40b1e336cf42b as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31471,"byte_end":31520,"line_start":696,"line_end":696,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let bhs_cuj_bmz_qky_pcao = encode_pointer(0x715bdee479e8dd12 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:696:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m696\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let bhs_cuj_bmz_qky_pcao = encode_pointer(0x715bdee479e8dd12 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31558,"byte_end":31607,"line_start":697,"line_end":697,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let nbty_rzi_juc_loz_hpx = encode_pointer(0xb71eadca34c08091 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:697:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m697\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let nbty_rzi_juc_loz_hpx = encode_pointer(0xb71eadca34c08091 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31645,"byte_end":31694,"line_start":698,"line_end":698,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let oa_awy_lpv_cip_gbueo = encode_pointer(0xbe27cf77b55b034e as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:698:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m698\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let oa_awy_lpv_cip_gbueo = encode_pointer(0xbe27cf77b55b034e as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31732,"byte_end":31781,"line_start":699,"line_end":699,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let rfl_fmi_vpu_cbb_jmaj = encode_pointer(0x765feb2a8ce63857 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:699:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m699\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let rfl_fmi_vpu_cbb_jmaj = encode_pointer(0x765feb2a8ce63857 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31819,"byte_end":31868,"line_start":700,"line_end":700,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let efs_jsyq_btd_ety_jxg = encode_pointer(0x84cb615fd01cf69e as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:700:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m700\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let efs_jsyq_btd_ety_jxg = encode_pointer(0x84cb615fd01cf69e as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31905,"byte_end":31954,"line_start":701,"line_end":701,"column_start":35,"column_end":84,"is_primary":true,"text":[{"text":"        let beyi_udt_clmu_jgbdm = encode_pointer(0x09716e27f9311036 as *mut c_void);","highlight_start":35,"highlight_end":84}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:701:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m701\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let beyi_udt_clmu_jgbdm = encode_pointer(0x09716e27f9311036 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":31992,"byte_end":32041,"line_start":702,"line_end":702,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let yal_bwy_ebz_oki_yahf = encode_pointer(0x93d6253af1385f66 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:702:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m702\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let yal_bwy_ebz_oki_yahf = encode_pointer(0x93d6253af1385f66 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":32079,"byte_end":32128,"line_start":703,"line_end":703,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let qow_pmw_xyq_jbd_znyp = encode_pointer(0x9ed07f2ffa67506b as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:703:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m703\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let qow_pmw_xyq_jbd_znyp = encode_pointer(0x9ed07f2ffa67506b as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":32166,"byte_end":32215,"line_start":704,"line_end":704,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let gnv_poez_bsg_xpd_gal = encode_pointer(0x9fc5326fbd6b4f7d as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:704:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m704\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let gnv_poez_bsg_xpd_gal = encode_pointer(0x9fc5326fbd6b4f7d as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":32253,"byte_end":32302,"line_start":705,"line_end":705,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let bzx_bcov_bsv_eyz_feo = encode_pointer(0xd8d17871e82f1c2c as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:705:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m705\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let bzx_bcov_bsv_eyz_feo = encode_pointer(0xd8d17871e82f1c2c as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":32340,"byte_end":32389,"line_start":706,"line_end":706,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let lcy_alrx_tms_zog_klt = encode_pointer(0x9fd26e71e62f186b as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:706:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m706\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let lcy_alrx_tms_zog_klt = encode_pointer(0x9fd26e71e62f186b as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":32427,"byte_end":32476,"line_start":707,"line_end":707,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let gika_pmg_faw_wpm_qgq = encode_pointer(0xc28b622bec300c6b as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:707:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m707\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let gika_pmg_faw_wpm_qgq = encode_pointer(0xc28b622bec300c6b as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":32514,"byte_end":32563,"line_start":708,"line_end":708,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let xqg_ryst_fec_tjl_puc = encode_pointer(0x84cd6834a42f1028 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:708:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m708\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let xqg_ryst_fec_tjl_puc = encode_pointer(0x84cd6834a42f1028 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":32601,"byte_end":32650,"line_start":709,"line_end":709,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let mqg_ocpe_qbb_pvv_ufc = encode_pointer(0x9a8b5936ea365a76 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:709:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m709\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mqg_ocpe_qbb_pvv_ufc = encode_pointer(0x9a8b5936ea365a76 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":32688,"byte_end":32737,"line_start":710,"line_end":710,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let eee_zia_jmr_cwo_apsu = encode_pointer(0xc6f66433e5731625 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:710:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m710\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let eee_zia_jmr_cwo_apsu = encode_pointer(0xc6f66433e5731625 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":32775,"byte_end":32824,"line_start":711,"line_end":711,"column_start":36,"column_end":85,"is_primary":true,"text":[{"text":"        let qri_wtvd_abi_zcs_puq = encode_pointer(0xd8c97b6bab5d7f90 as *mut c_void);","highlight_start":36,"highlight_end":85}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `encode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:711:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m711\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let qri_wtvd_abi_zcs_puq = encode_pointer(0xd8c97b6bab5d7f90 as *mut c_void);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"call to unsafe function `decode_pointer` is unsafe and requires unsafe function or block","code":{"code":"E0133","explanation":"Unsafe code was used outside of an unsafe block.\n\nErroneous code example:\n\n```compile_fail,E0133\nunsafe fn f() { return; } // This is the unsafe code\n\nfn main() {\n    f(); // error: call to unsafe function requires unsafe function or block\n}\n```\n\nUsing unsafe functionality is potentially dangerous and disallowed by safety\nchecks. Examples:\n\n* Dereferencing raw pointers\n* Calling functions via FFI\n* Calling functions marked unsafe\n\nThese safety checks can be relaxed for a section of the code by wrapping the\nunsafe instructions with an `unsafe` block. For instance:\n\n```\nunsafe fn f() { return; }\n\nfn main() {\n    unsafe { f(); } // ok!\n}\n```\n\nSee the [unsafe section][unsafe-section] of the Book for more details.\n\n#### Unsafe code in functions\n\nUnsafe code is currently accepted in unsafe functions, but that is being phased\nout in favor of requiring unsafe blocks here too.\n\n```\nunsafe fn f() { return; }\n\nunsafe fn g() {\n    f(); // Is accepted, but no longer recommended\n    unsafe { f(); } // Recommended way to write this\n}\n```\n\nLinting against this is controlled via the `unsafe_op_in_unsafe_fn` lint, which\nis `warn` by default in the 2024 edition and `allow` by default in earlier\neditions.\n\n[unsafe-section]: https://doc.rust-lang.org/book/ch19-01-unsafe-rust.html\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":34265,"byte_end":34296,"line_start":731,"line_end":731,"column_start":35,"column_end":66,"is_primary":true,"text":[{"text":"            let decoded_segment = decode_pointer(encoded_segment) as u64;","highlight_start":35,"highlight_end":66}],"label":"call to unsafe function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"consult the function's documentation for information on how to avoid undefined behavior","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0133]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: call to unsafe function `decode_pointer` is unsafe and requires unsafe function or block\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:731:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m731\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let decoded_segment = decode_pointer(encoded_segment) as u64;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcall to unsafe function\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consult the function's documentation for information on how to avoid undefined behavior\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 57 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 57 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0133`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0133`.\u001b[0m\n"}
